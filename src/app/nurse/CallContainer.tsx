import React, { useRef, useState } from "react";
import { Dimensions, Platform, ScrollView, View } from "react-native";
import NurseCallNative from "./call";
import CallDetails from "./calldetails";

// Get device width for paging
const { width: windowWidth } = Dimensions.get("window");

const CallContainer: React.FC = () => {
  const scrollViewRef = useRef<ScrollView>(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [callEnded, setCallEnded] = useState(false);

  const isIpad = Platform.OS === "ios" && (Platform as any).isPad === true;

  const onMomentumScrollEnd = (event: any) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    setActiveIndex(Math.round(offsetX / windowWidth));
  };

  const goToCall = () => {
    scrollViewRef.current?.scrollTo({ x: 0, animated: true });
  };

  const goToDetails = () => {
    scrollViewRef.current?.scrollTo({ x: windowWidth, animated: true });
  };

  const endCall = () => {
    setCallEnded(true);
  };

  if (isIpad) {
    // No horizontal paging on iPad; just render the call screen.
    return (
      <View style={{ flex: 1 }}>
        <NurseCallNative />
      </View>
    );
  }

  return (
    <ScrollView
      ref={scrollViewRef}
      horizontal
      pagingEnabled
      showsHorizontalScrollIndicator={false}
      onMomentumScrollEnd={onMomentumScrollEnd}
    >
      {/* The Call view */}
      <View style={{ width: windowWidth, flex: 1 }}>
        <NurseCallNative />
      </View>

      {/* The Call Details view */}
      <View style={{ width: windowWidth, flex: 1 }}>
        <CallDetails goToCall={goToCall} endCall={endCall} isFromCall={true} />
      </View>
    </ScrollView>
  );
};

export default CallContainer;


