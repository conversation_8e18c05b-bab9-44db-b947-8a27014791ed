import React, { useRef, useState } from "react";
import { Platform, ScrollView, View, useWindowDimensions } from "react-native";
import NurseCallNative from "./call";
import CallDetails from "./calldetails";

const CallContainer: React.FC = () => {
  const { width, height } = useWindowDimensions();
  const isLandscape = width > height;
  const scrollViewRef = useRef<ScrollView>(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [callEnded, setCallEnded] = useState(false);

  const isIpad = Platform.OS === "ios" && (Platform as any).isPad === true;

  // Use current window width for proper paging
  const windowWidth = width;

  const onMomentumScrollEnd = (event: any) => {
    const offsetX = event.nativeEvent.contentOffset.x;
    setActiveIndex(Math.round(offsetX / windowWidth));
  };

  const goToCall = () => {
    scrollViewRef.current?.scrollTo({ x: 0, animated: true });
  };

  const goToDetails = () => {
    scrollViewRef.current?.scrollTo({ x: windowWidth, animated: true });
  };

  const endCall = () => {
    setCallEnded(true);
  };

  // Always render the same structure to prevent re-mounting
  // Use conditional styling and behavior instead
  const shouldUseScrolling = !isIpad || !isLandscape;

  return (
    <View style={{ flex: 1 }}>
      {shouldUseScrolling ? (
        // iPad Portrait or Mobile: Use horizontal scrolling navigation
        <ScrollView
          ref={scrollViewRef}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={onMomentumScrollEnd}
          style={{ flex: 1 }}
        >
          {/* The Call view */}
          <View style={{ width: windowWidth, flex: 1 }}>
            <NurseCallNative goToDetails={goToDetails} />
          </View>

          {/* The Call Details view */}
          <View style={{ width: windowWidth, flex: 1 }}>
            <CallDetails goToCall={goToCall} endCall={endCall} isFromCall={true} />
          </View>
        </ScrollView>
      ) : (
        // iPad Landscape: No horizontal paging, just render the call screen with side-by-side layout
        <NurseCallNative />
      )}
    </View>
  );
};

export default CallContainer;


