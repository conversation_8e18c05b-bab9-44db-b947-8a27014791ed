import { RouteProp, useRoute } from "@react-navigation/native";
import { useRouter } from "expo-router";
import React from "react";
import NurseCallWrapper from "../../components/NurseCallComponent";
import axiosConfig from "../../services/axiosConfig";
import { View } from "tamagui";

type NurseCallRouteParams = {
  params: {
    consultationId: string;
    sdkId: string;
    rejoin?: string;
  };
};

interface NurseCallPageProps {
  goToDetails?: () => void;
}

const NurseCallPage: React.FC<NurseCallPageProps> = ({ goToDetails }) => {
  const router = useRouter();
  const route = useRoute<RouteProp<NurseCallRouteParams, "params">>();
  const { consultationId, sdkId, rejoin } = route.params;

  const handleCallEnd = () => {
    router.replace({
      pathname: "/nurse/calloverview",
      params: { consultationId },
    });
  };

  const handleCallOverview = () => {
    router.replace({
      pathname: "/nurse/calloverview",
      params: { consultationId },
    });
  };

  const handleRetryRequest = async (): Promise<string> => {
    try {
      const url = `/consultation/rejoin-request/${consultationId}`;
      const response = await axiosConfig.get(url);
      const { nurseSDKJWT } = response.data;
      return nurseSDKJWT;
    } catch (error) {
      console.error("Failed to get new SDK token:", error);
      throw error;
    }
  };

  return (
   <View  {...style.container}>
        <NurseCallWrapper
      consultationId={consultationId}
      sdkToken={sdkId}
      onCallEnd={handleCallEnd}
      onCallOverview={handleCallOverview}
      onRetryRequest={handleRetryRequest}
      goToDetails={goToDetails}
    />
   </View>
  );
};
const style = {

  container: { flex: 1, backgroundColor: "#0C111D", paddingInline: 20 },

};

export default NurseCallPage;
