import { useTheme } from "@/_layout";
import {
  ChevronDown,
  CircleCheckBig,
  FileText,
  MessageSquare,
  PhoneCall,
  Plus,
} from "@tamagui/lucide-icons";
import { isAxiosError } from "axios";
import { useRouter } from "expo-router";
import React, { useEffect, useState } from "react";
import { Linking, Modal, TouchableOpacity } from "react-native";
import {
  Accordion,
  Button,
  Spinner,
  Square,
  Text,
  View,
  XStack,
  YStack,
} from "tamagui";
import { useAuth } from "~/context/AuthContext";
import axiosConfig from "~/services/axiosConfig";
import { displayPhoneNumber } from "../utils/utils";
import { Badge } from "./Badge";
import { CallPendingDialog } from "./CallPendingDialog";
import { HorizontalDashedLine } from "./DashedLine";
import { DialogBox } from "./Dialog";
import { Platform } from "react-native";

export const Consultation = ({
  data,
  isFromProvider,
  isFromCallScreen,
  isFromCallOverViewScreen = false,
  onFinishLaterPress,
  onClosePress,
  shouldShowDay = false,
  shouldSowOrderandBadge = true,
}: {
  data: any;
  isFromProvider: boolean;
  isFromCallScreen: boolean;
  isFromCallOverViewScreen?: boolean;
  onFinishLaterPress?: () => void;
  onClosePress?: () => void;
  shouldShowDay?: boolean;
  shouldSowOrderandBadge?: boolean;
}) => {
  const [showRejoinErrorDialog, setShowRejoinErrorDialog] = useState(false);
  const [showFollowUpDialog, setShowFollowUpDialog] = useState(false);
  const router = useRouter();
  const styles = getStyles();
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";
  const [isOrderConfirmed, setIsOrderConfirmed] = useState(
    data.order_confirmed
  );
  const [day, setDay] = useState<string>("");
  const {
    id,
    full_name,
    dob,
    gender,
    date,
    time,
    order,
    status,
    chief_complaint,
    order_confirmed,
    facility_name,
    facility_phone_number,
    nurse_name,
    consultation_request_id,
  } = data;

  const hasOrder = order && order !== "Waiting for Provider to place order";

  const [isLessThan30Min, setIsLessThan30Min] = useState(false);
  const [isLessThan10Min, setIsLessThan10Min] = useState(false);
  const { user } = useAuth();

  const isProviderAndNoOrder =
    user?.role === "provider" &&
    !hasOrder &&
    (status !== "Submitted" || status === "Non Billable");
  const [isOpen, setIsOpen] = useState(
    isFromCallOverViewScreen || isProviderAndNoOrder ? true : false
  );

  useEffect(() => {
    try {
      const [y, m, d] = date.split("-").map(Number);
      const targetDate = new Date(y, m - 1, d);

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      targetDate.setHours(0, 0, 0, 0);

      const diffInMs = today.getTime() - targetDate.getTime();
      const diffInDays = Math.round(diffInMs / 86400000); // ms in a day

      if (diffInDays >= 0 && diffInDays <= 5) {
        const weekday = targetDate.toLocaleDateString("en-US", {
          weekday: "short",
        });
        setDay(weekday);
      } else {
        const formatted = [
          String(targetDate.getMonth() + 1).padStart(2, "0"),
          String(targetDate.getDate()).padStart(2, "0"),
        ].join("/");
        setDay(formatted);
      }
    } catch (error) {}
  }, [date]);

  useEffect(() => {
    try {
      if (!date || !time) {
        setIsLessThan30Min(false);
        setIsLessThan10Min(false);
        console.log("Missing date or time:", { date, time });
        return;
      }

      const [y, m, d] = date.split("-").map(Number);
      const timeParts = time.split(":").map(Number);
      const hour = timeParts[0] || 0;
      const minute = timeParts[1] || 0;
      const second = timeParts[2] || 0;

      const consultationDate = new Date(
        Date.UTC(y, m - 1, d, hour, minute, second)
      );
      const now = new Date();
      const diffMs = now.getTime() - consultationDate.getTime();
      const diffMin = diffMs / 60000;

      // setIsLessThan30Min(diffMin >= 0 && diffMin <= 30);
      setIsLessThan10Min(diffMin >= 0 && diffMin <= 10);
    } catch (error) {
      setIsLessThan30Min(false);
      setIsLessThan10Min(false);
    }
  }, [date, time]);

  useEffect(() => {
    setIsOpen(isFromCallOverViewScreen ? true : false);
  }, [isFromCallScreen, isFromCallOverViewScreen]);

  if (!data) return null;

  const onViewChatPress = () => {
    router.push({ pathname: "/nurse/chat", params: { consultationId: id } });
  };
  const onReviewCallPress = () => {
    router.push({
      pathname: "/provider/reviewcall",
      params: { consultationId: id },
    });
  };

  const onRejoinCall = async () => {
    try {
      const consultationId = id;
      const url = `/consultation/rejoin-request/${consultationId}`;
      const response = await axiosConfig.get(url);
      const { nurseSDKJWT, providerSDKJWT } = response.data;
      if (user?.role === "nurse") {
        const isIpad = Platform.OS === "ios" && (Platform as any).isPad === true;
        const pathname = isIpad ? "/nurse/call" : ("/nurse/CallContainer" as const);
        router.push({
          // @ts-expect-error allow nurse CallContainer route string
          pathname,
          params: { consultationId: id, sdkId: nurseSDKJWT, rejoin: "true" },
        });
      } else {
        router.push({
          pathname: "/provider/CallContainer",
          params: { consultationId: id, sdkId: providerSDKJWT },
        });
      }
    } catch (error) {
      if (isAxiosError(error) && error.response?.status === 400) {
        setShowRejoinErrorDialog(true);
      }
    }
  };

  const followUpCall = async () => {
    try {
      setShowFollowUpDialog(true);
    } catch (error) {}
  };

  const onConfirmOrder = async () => {
    const response = await axiosConfig.put(`/consultation/${id}/order/confirm`);
    if (response.status === 200) {
      setIsOrderConfirmed(true);
    }
    if (response.status === 400) {
      setIsOrderConfirmed(false);
    }
  };

  const canRejoinCall =
    isLessThan10Min &&
    status &&
    status === "Started" &&
    !isFromCallOverViewScreen;

  return (
    <View {...styles.container}>
      <Accordion
        overflow="hidden"
        borderWidth={1}
        type="multiple"
        {...styles.accordion}
        borderColor={isDarkMode ? "#697586" : "#D0D5DD"}
        value={canRejoinCall || isOpen ? ["a1"] : []}
      >
        <Accordion.Item value="a1">
          <Accordion.Trigger
            {...styles.trigger}
            onPress={() => setIsOpen(!isOpen)}
          >
            {({ open }: { open: boolean }) => (
              <XStack {...styles.headerRow}>
                <YStack>
                  <XStack {...styles.nameContainer}>
                    <XStack maxW={210} marginInlineEnd={5}>
                      {shouldShowDay && (
                        <>
                          <Text {...styles.dayText}>{day}</Text>
                          <View {...styles.verticalIndicator} />
                        </>
                      )}
                      <Text
                        style={{
                          flexShrink: 1,
                          flexWrap: "wrap",
                        }}
                        {...styles.nameText}
                      >
                        {full_name}
                      </Text>
                    </XStack>

                    {!isFromCallOverViewScreen &&
                      shouldSowOrderandBadge &&
                      isFromProvider && (
                        <View>
                          <Badge
                            status={
                              status === "Started" || status === "Completed"
                                ? "Pending"
                                : status
                            }
                          />
                        </View>
                      )}
                  </XStack>

                  <XStack {...styles.detailsRow}>
                    <Text {...styles.detailText}>Gender: {gender}</Text>
                    <Text {...styles.separator}>|</Text>
                    <Text {...styles.detailText}>DOB: {dob}</Text>
                  </XStack>
                </YStack>
                <Square animation="quick" rotate={open ? "180deg" : "0deg"}>
                  <ChevronDown size="$1" />
                </Square>
              </XStack>
            )}
          </Accordion.Trigger>
          <Accordion.HeightAnimator animation="medium">
            <Accordion.Content {...styles.content}>
              <YStack {...styles.innerContainer}>
                <View {...styles.divider} />
                {facility_name && user?.role !== "nurse" && (
                  <YStack marginBlockStart={15} maxW={210}>
                    <Text
                      style={{
                        flexShrink: 1,
                        flexWrap: "wrap",
                      }}
                      {...styles.facilityNameText}
                    >
                      {facility_name}
                    </Text>
                    {nurse_name && (
                      <Text
                        style={{
                          flexShrink: 1,
                          flexWrap: "wrap",
                        }}
                        {...styles.facilityNameText}
                      >
                        Nurse: {nurse_name}
                      </Text>
                    )}
                    <TouchableOpacity
                      onPress={() => {
                        if (facility_phone_number) {
                          Linking.openURL(`telprompt:${facility_phone_number}`);
                        }
                      }}
                    >
                      <Text
                        style={{
                          flexShrink: 1,
                          flexWrap: "wrap",
                          textDecorationLine: "underline",
                          color: "#007AFF", // iOS-style link color
                        }}
                        {...styles.facilityNameText}
                      >
                        {displayPhoneNumber(facility_phone_number)}
                      </Text>
                    </TouchableOpacity>
                  </YStack>
                )}
                <YStack>
                  <YStack {...styles.section}>
                    <Text {...styles.sectionTitle}>Date:</Text>
                    <Text {...styles.sectionText}>
                      {time}, {date}
                    </Text>
                  </YStack>
                  <YStack {...styles.section}>
                    <Text {...styles.sectionTitle}>Chief Complaint:</Text>
                    <Text {...styles.sectionText}>{chief_complaint}</Text>
                  </YStack>
                  {user?.role === "nurse" && (
                    <YStack {...styles.section}>
                      <Text {...styles.sectionTitle}>Provider:</Text>
                      <Text {...styles.sectionText}>
                        {data.provider_name || "Unavailable"}
                      </Text>
                    </YStack>
                  )}
                  {shouldSowOrderandBadge && (
                    <YStack {...styles.section}>
                      <Text {...styles.sectionTitle}>Order:</Text>
                      <Text {...styles.sectionText}>
                        {!order && (
                          <Spinner
                            size="small"
                            color={isDarkMode ? "#697586" : "#D0D5DD"}
                            style={{
                              display: hasOrder ? "none" : "flex",
                              marginInlineEnd: 5,
                            }}
                          />
                        )}
                        {order
                          ? order
                          : "Order Pending - Waiting for Provider to place order"}
                      </Text>
                    </YStack>
                  )}
                </YStack>
                {!isFromCallScreen && (
                  <YStack marginBlockStart={15}>
                    <View {...styles.divider} />
                  </YStack>
                )}
                {!isFromCallScreen && (
                  <YStack {...styles.buttonContainer}>
                    {canRejoinCall && (
                      <Button
                        {...styles.rejoinCallbtn}
                        icon={<PhoneCall size={"$1"} />}
                        onPress={onRejoinCall}
                      >
                        Rejoin Call
                      </Button>
                    )}
                    {isProviderAndNoOrder && (
                      <Button
                        {...styles.rejoinCallbtn}
                        icon={<Plus size={"$1"} />}
                        onPress={onReviewCallPress}
                      >
                        Add Order
                      </Button>
                    )}
                    {!isFromProvider ? (
                      <>
                        <Button
                          {...styles.viewTranscriptButton}
                          icon={<MessageSquare size={"$1"} />}
                          onPress={onViewChatPress}
                        >
                          View chat
                        </Button>
                        {!isOrderConfirmed && (
                          <Button
                            disabled={!hasOrder}
                            {...styles.confirmOrderButton}
                            style={!hasOrder ? styles.iconDisabled : {}}
                            icon={
                              <CircleCheckBig
                                size={"$1"}
                                color={
                                  hasOrder
                                    ? "$confirmOrderTextColor"
                                    : "#D0D5DD"
                                }
                              />
                            }
                            onPress={() => onConfirmOrder()}
                          >
                            Confirm Order
                          </Button>
                        )}
                        {isOrderConfirmed && (
                          <Button
                            disabled={true}
                            {...styles.orderConfirmedBtn}
                            icon={<CircleCheckBig size={"$1"} />}
                          >
                            Order Confirmed
                          </Button>
                        )}
                        {isFromCallOverViewScreen && (
                          <Button
                            {...styles.confirmOrderButton}
                            onPress={
                              !isOrderConfirmed
                                ? onFinishLaterPress
                                : onClosePress
                            }
                          >
                            {isOrderConfirmed ? "Close" : "Finish Later"}
                          </Button>
                        )}
                      </>
                    ) : (
                      <Button
                        {...styles.reviewCallBtn}
                        icon={<FileText size={"$1"} />}
                        onPress={onReviewCallPress}
                      >
                        Review Call
                      </Button>
                    )}

                    {/* {isLessThan30Min &&
                      user?.role === "nurse" &&
                      !isFromCallOverViewScreen && (
                        <Button
                          {...styles.rejoinCallbtn}
                          icon={<PhoneCall size={"$1"} />}
                          onPress={followUpCall}
                        >
                          Follow up call
                        </Button>
                      )} */}
                  </YStack>
                )}
              </YStack>
            </Accordion.Content>
          </Accordion.HeightAnimator>
        </Accordion.Item>
      </Accordion>
      {!isFromCallOverViewScreen && !isFromCallScreen && isOpen && (
        <HorizontalDashedLine
          height={1}
          dashLength={2}
          dashGap={2}
          color="#D2D6DB"
          style={{ marginTop: 18 }}
        />
      )}
      <Modal visible={showRejoinErrorDialog} transparent animationType="fade">
        <View
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flex: 1,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            height: "100%",
            width: "100%",
          }}
        >
          <DialogBox
            open={showRejoinErrorDialog}
            onClose={() => setShowRejoinErrorDialog(false)}
            title="Rejoin Failed"
            body="Consultation is not available to rejoin."
            btnText="OK"
            showCloseBtn={false}
          />
        </View>
      </Modal>
      {showFollowUpDialog && (
        <CallPendingDialog
          open={showFollowUpDialog}
          onClose={() => setShowFollowUpDialog(false)}
          consultation_request_id={consultation_request_id}
        />
      )}
    </View>
  );
};

const getStyles = () => {
  return {
    container: {
      marginBlockStart: 10,
    },
    accordion: {
      borderRadius: "$6" as "$6",
    },
    trigger: {
      flexDirection: "row" as "row",
      borderWidth: 0,
      backgroundColor: "$screenBackgroundcolor",
    },
    headerRow: {
      justifyContent: "space-between",
      flex: 1,
    },
    nameText: {
      fontSize: 16,
      fontWeight: "600" as any,
      marginInlineEnd: 10,
      color: "$textcolor" as any,
      maxWidth: 220,
    },
    facilityNameText: {
      fontSize: 16,
      marginInlineEnd: 10,
      color: "$textcolor" as any,
      maxWidth: 220,
    },
    detailsRow: {
      marginBlockStart: 5,
    },
    detailText: {
      fontSize: 14,
      fontWeight: "400" as any,
      marginInlineEnd: 10,
      color: "$textcolor" as any,
    },
    separator: {
      fontSize: 16,
      fontWeight: "200" as "200",
      marginInlineEnd: 10,
    },
    content: {
      animation: "medium" as "medium",
      exitStyle: { opacity: 0 },
      backgroundColor: "$screenBackgroundcolor",
    },
    innerContainer: {
      marginBlockStart: -20,
    },
    divider: {
      height: 1,
      backgroundColor: "$primaryBorderColor",
    },
    section: {
      marginBlockStart: 15,
    },
    sectionTitle: {
      fontSize: 14,
      fontWeight: "500" as any,
      color: "$textcolor" as any,
    },
    sectionText: {
      fontSize: 14,
      fontWeight: "400" as any,
      marginBlockStart: 7,
      color: "$textcolor" as any,
    },
    buttonContainer: {
      marginBlockStart: 20,
    },
    viewTranscriptButton: {
      fontSize: 16,
      size: "$4" as any,
      fontWeight: "600" as any,
      backgroundColor: "$screenBackgroundcolor",
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
    },
    iconDisabled: {
      color: "#D0D5DD",
    },
    confirmOrderButton: {
      marginBlockStart: 10,
      fontSize: 16,
      size: "$4" as any,
      fontWeight: "600" as any,
      backgroundColor: "$confirmOrderBlue",
      borderColor: "$confirmOrderBorderCOlor" as any,
      color: "$confirmOrderTextColor" as any,
      borderWidth: 1,
    },
    orderConfirmedBtn: {
      marginBlockStart: 10,
      fontSize: 16,
      size: "$4" as any,
      fontWeight: "600" as any,
      backgroundColor: "$orderConfirmedBackground",
      borderColor: "$primaryBorderColor" as any,
      color: "$orderConfirmedTextColor" as any,
      borderWidth: 1,
    },
    reviewCallBtn: {
      fontSize: 16,
      size: "$4" as any,
      fontWeight: "600" as any,
      backgroundColor: "$confirmOrderBlue",
      borderColor: "$confirmOrderBorderCOlor" as any,
      color: "$confirmOrderTextColor" as any,
      borderWidth: 1,
    },
    dottedLineContainer: {
      width: "100%" as "100%",
      alignItems: "flex-start",
      marginBlockStart: 10,
    },
    dottedLine: {
      flexDirection: "row" as "row",
      width: "100%" as "100%",
      height: 2,
      backgroundColor: "transparent" as "transparent",
    },
    dot: {
      width: 5,
      height: 2,
      backgroundColor: "$primaryBorderColor",
      marginRight: 5,
    },
    verticalIndicator: {
      width: 1,
      height: 16,
      backgroundColor: "$textcolor",
      marginInline: 5,
    },
    dayText: {
      fontSize: 16,
      fontWeight: "600" as any,
      color: "$textcolor" as any,
    },
    nameContainer: {
      alignItems: "center" as any,
      flexWrap: "wrap" as any,
    },
    rejoinCallbtn: {
      backgroundColor: "$primaryColor",
      color: "$buttonWhiteColor" as any,
      fontSize: 16,
      size: "$4" as any,
      fontWeight: "600" as any,
      marginBlockEnd: 10,
    },
  };
};

export default Consultation;
