import { FlatList } from "react-native";
import { Plus } from "@tamagui/lucide-icons";
import { Button, Text, YStack } from "tamagui";
import { demoPatients } from "@/nurse/calldetails";



export const PatientQueue = ({
  patients,
  setDialogOpen,
  renderItem,
  endCall,
}: {
  patients: demoPatients[];
  setDialogOpen: React.Dispatch<React.SetStateAction<boolean>>;
  renderItem: (item: demoPatients) => JSX.Element;
  endCall: () => void;
}) => (
  <>
    <Button
      {...styles.addToQueueBtn}
      icon={<Plus size={"$1"} color={"$confirmOrderTextColor"} />}
      onPress={() => setDialogOpen(true)}
    >
      Add to queue
    </Button>

    <Text {...styles.patientQueueText}>PATIENT QUEUE ({patients.length})</Text>

    <YStack flex={1}>
      <FlatList
        data={patients}
        renderItem={({ item }) => renderItem(item)}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
      />
    </YStack>

    <Button {...styles.endCallBtn} onPress={endCall}>
      End Call
    </Button>
  </>
);

const styles = {
  addToQueueBtn: {
    marginBlockStart: 20,
    fontSize: 16,
    size: "$4" as any,
    fontWeight: "600" as any,
    backgroundColor: "$confirmOrderBlue",
    borderColor: "$confirmOrderBorderCOlor" as any,
    color: "$confirmOrderTextColor" as any,
    borderWidth: 1,
  },
  patientQueueText: {
    fontSize: 14,
    fontWeight: "600" as any,
    color: "$textcolor" as any,
    marginBlockStart: 30,
    marginBlockEnd: 20,
  },
  endCallBtn: {
    backgroundColor: "$primaryColor" as any,
    color: "$buttonWhiteColor" as any,
    fontWeight: "600" as any,
    marginBlockStart: 10,
    size: "$4" as any,
    fontSize: 16,
    marginBlockEnd: 10,
  },
};
