import axios from "axios";
import { EventEmitter } from "events";
import * as SecureStore from "expo-secure-store";
import Constants from "expo-constants";
export const eventEmitter = new EventEmitter();

const instance = axios.create({
  // baseURL: `http://************:3008/api/v1`,
  //  baseURL: `${Constants.expoConfig?.extra?.apiUrl}/api/v1`,
  // baseURL: `http://localhost:3008/api/v1`,
   baseURL: "https://www.api.vitalcare.org/api/v1",
  headers: {
    "Content-Type": "application/json",
  },
  timeout: 20000,
});

instance.interceptors.request.use(
  async (config) => {
    const authToken = await SecureStore.getItemAsync("authToken");
    if (authToken && config.headers) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    const deviceUUID = await SecureStore.getItemAsync("deviceUUID");
    if (deviceUUID) {
      if (config.method === "post") {
        config.data = {
          ...(typeof config.data === "object" && config.data
            ? config.data
            : {}),
          device_name: deviceUUID,
        };
      }
      if (config.method === "put") {
        config.data = {
          ...(typeof config.data === "object" && config.data
            ? config.data
            : {}),
          device_name: deviceUUID,
        };
      }
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor to handle 401 errors.
instance.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.config) {
      console.error("Error endpoint:", error.config.url);
      if (error.config.url && error.config.url.includes("auth/login")) {
        await SecureStore.deleteItemAsync("authToken");
        return Promise.reject(error);
      }
    }
    if (error.response && error?.response?.status === 401) {
      await SecureStore.deleteItemAsync("authToken");
      eventEmitter.emit("logout");
    }
    return Promise.reject(error);
  }
);

export default instance;
